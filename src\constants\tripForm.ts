// src/constants/tripForm.ts

export const characteristicOptions = [
  'Strade sterrate',
  'Curve strette',
  'Presenza pedaggi',
  'Presenza traghetti',
  'Autostrada',
  'Bel paesaggio',
  'Visita prolungata',
  'Interesse gastronomico',
  'Interesse storico-culturale'
];

export const formFieldClasses = {
  input: "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm",
  textarea: "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm",
  select: "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm",
  label: "block text-sm font-medium text-gray-700",
  error: "text-xs text-red-500 mt-1",
  checkbox: "h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded",
  tagButton: "px-4 py-2 bg-primary-600 text-white font-medium rounded-r-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500",
  tagInput: "flex-grow px-3 py-2 border border-gray-300 rounded-l-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm",
  tagSpan: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800",
  tagRemoveButton: "ml-1.5 flex-shrink-0 inline-flex text-primary-500 hover:text-primary-700 focus:outline-none focus:text-primary-700"
};
