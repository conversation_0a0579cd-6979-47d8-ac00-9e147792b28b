@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 17, 24, 39;
  --background-start-rgb: 255, 255, 255;
  --background-end-rgb: 249, 250, 251;
}

body {
  color: rgb(var(--foreground-rgb));
  background: rgb(var(--background-start-rgb));
}

@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-semibold py-2 px-4 rounded-md transition-colors;
  }
  
  .btn-secondary {
    @apply bg-secondary-700 hover:bg-secondary-800 text-white font-semibold py-2 px-4 rounded-md transition-colors;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-md p-6;
  }
}