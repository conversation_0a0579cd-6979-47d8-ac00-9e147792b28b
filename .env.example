# NextAuth.js Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key-here

# Google OAuth Credentials
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Email Configuration (SMTP)
# Per Gmail: usa smtp.gmail.com, porta 587, e genera una password app
# Per altri provider: configura secondo le loro specifiche
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_FROM=<EMAIL>

# Database
DATABASE_URL=postgresql://username:password@host:port/database
BLOB_READ_WRITE_TOKEN="vercel_blob_rw_token_here"

# Stripe Configuration
# O<PERSON><PERSON> le chiavi dal tuo dashboard Stripe (https://dashboard.stripe.com/apikeys)
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here
