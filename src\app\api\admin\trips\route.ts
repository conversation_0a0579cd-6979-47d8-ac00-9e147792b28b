import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/core/prisma'
import { auth } from '@/auth'
import { UserRole } from '@/types/profile'

// Force dynamic rendering
export const dynamic = 'force-dynamic'

// GET - List all trips for admin management (only for Sentinel users)
export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Non autorizzato' },
        { status: 401 }
      )
    }

    if (session.user.role !== UserRole.Sentinel) {
      return NextResponse.json(
        { error: 'Permessi insufficienti' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const pageParam = searchParams.get('page') || '1'
    const limitParam = searchParams.get('limit') || '10'
    const page = isNaN(parseInt(pageParam)) ? 1 : parseInt(pageParam)
    const limit = isNaN(parseInt(limitParam)) ? 10 : parseInt(limitParam)
    const search = searchParams.get('search') || ''
    const statusFilter = searchParams.get('status') || ''

    const where: Record<string, unknown> = {}

    // Add search filter
    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { destination: { contains: search, mode: 'insensitive' } },
        { user: { name: { contains: search, mode: 'insensitive' } } }
      ]
    }

    // Add status filter
    if (statusFilter) {
      where.status = statusFilter
    }

    const skip = (page - 1) * limit

    // Get trips with pagination
    const [trips, total] = await Promise.all([
      prisma.trip.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true
            }
          }
        },
        orderBy: [
          {
            orderIndex: 'asc'
          },
          {
            created_at: 'desc'
          }
        ],
        skip,
        take: limit
      }),
      prisma.trip.count({ where })
    ])

    const pages = Math.ceil(total / limit)

    return NextResponse.json({
      trips,
      pagination: {
        page,
        limit,
        total,
        pages
      }
    })

  } catch (error) {
    console.error('Errore nel caricamento viaggi:', error)
    return NextResponse.json(
      { error: 'Errore interno del server' },
      { status: 500 }
    )
  }
}
